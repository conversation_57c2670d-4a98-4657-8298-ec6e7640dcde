<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Pending Users';

// Handle user approval/rejection
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $user_id = intval($_POST['user_id'] ?? 0);

    if ($user_id > 0 && in_array($action, ['approve', 'reject'])) {
        try {
            $db = getDB();

            // First, ensure the database schema supports the required status values
            try {
                $db->query("ALTER TABLE accounts MODIFY status ENUM('active', 'suspended', 'closed', 'pending', 'rejected') DEFAULT 'pending'");
            } catch (Exception $e) {
                // Schema might already be updated, continue
            }

            if ($action === 'approve') {
                // Approve user
                $db->query("UPDATE accounts SET status = 'active' WHERE id = ? AND status = 'pending'", [$user_id]);

                // Send approval email
                $user_result = $db->query("SELECT * FROM accounts WHERE id = ?", [$user_id]);
                $user = $user_result->fetch_assoc();

                if ($user) {
                    // Send approval email (implement email sending logic here)
                    $success_message = "User {$user['first_name']} {$user['last_name']} has been approved successfully.";
                }
            } else {
                // Reject user
                $db->query("UPDATE accounts SET status = 'rejected' WHERE id = ? AND status = 'pending'", [$user_id]);
                $success_message = "User has been rejected.";
            }

        } catch (Exception $e) {
            $error_message = "Failed to process user: " . $e->getMessage();
        }
    }
}

// Handle bulk operations
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['bulk_action']) && isset($_POST['user_ids'])) {
    $bulk_action = $_POST['bulk_action'];
    $user_ids = array_map('intval', $_POST['user_ids']);

    if (in_array($bulk_action, ['approve', 'reject']) && !empty($user_ids)) {
        try {
            $db = getDB();
            $processed_count = 0;

            foreach ($user_ids as $user_id) {
                if ($bulk_action === 'approve') {
                    $result = $db->query("UPDATE accounts SET status = 'active' WHERE id = ? AND status = 'pending'", [$user_id]);
                } else {
                    $result = $db->query("UPDATE accounts SET status = 'rejected' WHERE id = ? AND status = 'pending'", [$user_id]);
                }

                if ($result) {
                    $processed_count++;
                }
            }

            $success_message = "Successfully {$bulk_action}d {$processed_count} user(s).";

        } catch (Exception $e) {
            $error_message = "Failed to process bulk action: " . $e->getMessage();
        }
    }
}

// Pagination settings
$records_per_page = 20;
$current_page = intval($_GET['page'] ?? 1);
$offset = ($current_page - 1) * $records_per_page;

try {
    $db = getDB();
    
    // Get total count of pending users
    $count_result = $db->query("SELECT COUNT(*) as total FROM accounts WHERE status = 'pending' AND is_admin = 0");
    $total_records = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_records / $records_per_page);
    
    // Get pending users with pagination
    $users_query = "SELECT * FROM accounts 
                    WHERE status = 'pending' AND is_admin = 0 
                    ORDER BY created_at ASC 
                    LIMIT $records_per_page OFFSET $offset";
    
    $users_result = $db->query($users_query);
    $pending_users = [];
    while ($user = $users_result->fetch_assoc()) {
        $pending_users[] = $user;
    }
    
} catch (Exception $e) {
    $error_message = "Failed to load pending users: " . $e->getMessage();
    $pending_users = [];
    $total_records = 0;
    $total_pages = 0;
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Pending Users</li>
    </ol>
</nav>

<!-- Success/Error Messages -->
<?php if (isset($success_message)): ?>
<div class="alert alert-success" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-check-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($success_message); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error_message); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- New Modern Header Design -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="page-title mb-1">Pending Users</h2>
                <div class="text-muted">Pending User Approvals</div>
            </div>
            <div class="d-flex align-items-center gap-3">
                <span class="badge bg-warning text-dark px-3 py-2 fs-6">
                    <?php echo $total_records; ?> users awaiting approval
                </span>
                <?php if (!empty($pending_users)): ?>
                <div class="btn-group">
                    <button type="button" class="btn btn-success" onclick="bulkApprove()">
                        <i class="fas fa-check me-1"></i>
                        Bulk Approve
                    </button>
                    <button type="button" class="btn btn-danger" onclick="bulkReject()">
                        <i class="fas fa-times me-1"></i>
                        Bulk Reject
                    </button>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- User Approval Queue -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">User Approval Queue</h3>
                <?php if (!empty($pending_users)): ?>
                <div class="card-actions">
                    <div class="btn-group">
                        <button type="button" class="btn btn-success btn-sm" onclick="bulkApprove()">
                            Bulk Approve
                        </button>
                        <button type="button" class="btn btn-danger btn-sm" onclick="bulkReject()">
                            Bulk Reject
                        </button>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($pending_users)): ?>
                <div class="table-responsive">
                    <table class="table table-vcenter card-table">
                        <thead>
                            <tr>
                                <th class="w-1">
                                    <input type="checkbox" class="form-check-input" id="selectAll" onchange="toggleSelectAll()">
                                </th>
                                <th class="w-1">#</th>
                                <th>User Details</th>
                                <th>Contact Information</th>
                                <th>Registration Date</th>
                                <th>Pending Status</th>
                                <th class="w-1">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $row_number = ($current_page - 1) * $records_per_page + 1;
                            foreach ($pending_users as $user):
                                $waiting_days = floor((time() - strtotime($user['created_at'])) / (60 * 60 * 24));
                                $initials = strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1));
                            ?>
                            <tr>
                                <td>
                                    <input type="checkbox" class="form-check-input user-checkbox" value="<?php echo $user['id']; ?>">
                                </td>
                                <td>
                                    <span class="text-muted"><?php echo $row_number++; ?></span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-sm me-3" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                            <?php echo $initials; ?>
                                        </div>
                                        <div>
                                            <div class="fw-bold"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></div>
                                            <div class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div class="text-muted small">Email:</div>
                                        <div><?php echo htmlspecialchars($user['email']); ?></div>
                                    </div>
                                    <?php if (!empty($user['phone'])): ?>
                                    <div class="mt-1">
                                        <div class="text-muted small">Phone:</div>
                                        <div><?php echo htmlspecialchars($user['phone']); ?></div>
                                    </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div><?php echo date('M j, Y', strtotime($user['created_at'])); ?></div>
                                    <small class="text-muted"><?php echo date('g:i A', strtotime($user['created_at'])); ?></small>
                                </td>
                                <td>
                                    <span class="badge bg-warning text-dark">
                                        <?php echo $waiting_days; ?> days pending
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-success btn-sm"
                                                onclick="showConfirmation('approve', <?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>', '<?php echo htmlspecialchars($user['username']); ?>')"
                                                title="Approve User">
                                            Approve
                                        </button>
                                        <button type="button" class="btn btn-danger btn-sm"
                                                onclick="showConfirmation('reject', <?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>', '<?php echo htmlspecialchars($user['username']); ?>')"
                                                title="Reject User">
                                            Reject
                                        </button>
                                        <button type="button" class="btn btn-outline-primary btn-sm"
                                                onclick="viewUserDetails(<?php echo $user['id']; ?>)"
                                                title="View Details">
                                            View
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="empty">
                    <div class="empty-img">
                        <i class="fas fa-check-circle" style="font-size: 3rem; color: #28a745;"></i>
                    </div>
                    <p class="empty-title">No Pending Users</p>
                    <p class="empty-subtitle text-muted">
                        All user registrations have been processed. Great job!
                    </p>
                    <div class="empty-action">
                        <a href="users.php" class="btn btn-primary">
                            <i class="fas fa-users me-2"></i>
                            View All Users
                        </a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Compact Pagination -->
<?php if ($total_pages > 1): ?>
<div class="row mt-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <small class="text-muted">
                Page <?php echo $current_page; ?> of <?php echo $total_pages; ?>
                (<?php echo number_format($total_records); ?> total)
            </small>
            <nav aria-label="Pending users pagination">
                <ul class="pagination pagination-sm mb-0">
                    <?php if ($current_page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=1">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $current_page - 1; ?>">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php
                    $start_page = max(1, $current_page - 1);
                    $end_page = min($total_pages, $current_page + 1);

                    for ($i = $start_page; $i <= $end_page; $i++):
                    ?>
                    <li class="page-item <?php echo $i === $current_page ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?>">
                            <?php echo $i; ?>
                        </a>
                    </li>
                    <?php endfor; ?>

                    <?php if ($current_page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $current_page + 1; ?>">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $total_pages; ?>">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- User Details Modal -->
<div class="modal modal-blur fade" id="userDetailsModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, #4361ee 0%, #3730a3 100%); color: white; border-bottom: 3px solid rgba(255,255,255,0.1);">
                <h5 class="modal-title">
                    <i class="fas fa-user-circle me-2"></i>
                    User Account Details
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="userDetailsContent" style="background: rgba(67, 97, 238, 0.02);">
                <!-- User details will be loaded here -->
            </div>
            <div class="modal-footer" style="border-top: 1px solid rgba(67, 97, 238, 0.1);">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Close
                </button>
                <button type="button" class="btn btn-success" id="approveFromDetailsBtn" style="display: none;" onclick="approveUserFromDetails()">
                    <i class="fas fa-check me-2"></i>Approve User
                </button>
                <button type="button" class="btn btn-danger" id="rejectFromDetailsBtn" style="display: none;" onclick="rejectUserFromDetails()">
                    <i class="fas fa-times me-2"></i>Reject User
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Custom Confirmation Modal -->
<div class="modal modal-blur fade" id="confirmationModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header" id="confirmModalHeader">
                <h5 class="modal-title" id="confirmModalTitle">
                    <i class="fas fa-question-circle me-2"></i>
                    Confirm Action
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="confirmModalBody">
                <!-- Confirmation content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancel
                </button>
                <button type="button" class="btn" id="confirmActionBtn" onclick="executeConfirmedAction()">
                    <i class="fas fa-check me-2"></i>Confirm
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function viewUserDetails(userId) {
    // Find user data from the table
    const userRow = document.querySelector(`button[onclick="viewUserDetails(${userId})"]`).closest('tr');
    const userCells = userRow.querySelectorAll('td');

    // Extract user information from the table row
    const userNumber = userCells[0].textContent.trim();
    const userDetailsCell = userCells[1];
    const contactCell = userCells[2];
    const registrationCell = userCells[3];
    const statusCell = userCells[4];

    // Parse user details
    const avatar = userDetailsCell.querySelector('.avatar').textContent.trim();
    const nameElement = userDetailsCell.querySelector('.fw-bold');
    const usernameElement = userDetailsCell.querySelector('.text-muted');
    const fullName = nameElement ? nameElement.textContent.trim() : 'N/A';
    const username = usernameElement ? usernameElement.textContent.trim() : 'N/A';

    // Parse contact info
    const emailDiv = contactCell.querySelector('div:first-child div:last-child');
    const phoneDiv = contactCell.querySelector('div:last-child div:last-child');
    const email = emailDiv ? emailDiv.textContent.trim() : 'N/A';
    const phone = phoneDiv ? phoneDiv.textContent.trim() : 'Not provided';

    // Parse registration date
    const regDate = registrationCell.querySelector('div:first-child').textContent.trim();
    const regTime = registrationCell.querySelector('small').textContent.trim();

    // Parse status
    const pendingDays = statusCell.querySelector('small').textContent.trim();

    // Create user details HTML with enhanced design
    const userDetailsHTML = `
        <div class="row g-3">
            <div class="col-md-4">
                <div class="text-center">
                    <div class="avatar avatar-xl mx-auto mb-3" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; font-size: 1.5rem; border: 3px solid rgba(220, 53, 69, 0.2);">
                        ${avatar}
                    </div>
                    <h4 class="mb-1 fw-bold">${fullName}</h4>
                    <p class="text-muted mb-2">${username}</p>
                    <span class="badge bg-warning text-dark px-3 py-2">
                        <i class="fas fa-hourglass-half me-1"></i>Pending Approval
                    </span>
                    <div class="mt-3">
                        <small class="text-muted d-block">${pendingDays}</small>
                    </div>
                </div>
            </div>
            <div class="col-md-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-header py-2" style="background: linear-gradient(135deg, #4361ee 0%, #3730a3 100%); color: white; border-bottom: 3px solid rgba(255,255,255,0.1);">
                        <h3 class="card-title mb-0 h6">
                            <i class="fas fa-id-card me-2"></i>
                            Account Information
                        </h3>
                    </div>
                    <div class="card-body py-3" style="background: rgba(67, 97, 238, 0.02);">
                        <div class="row g-2">
                            <div class="col-sm-6">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-user text-primary me-2"></i>
                                    <div>
                                        <small class="text-muted d-block">Full Name</small>
                                        <strong>${fullName}</strong>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-at text-primary me-2"></i>
                                    <div>
                                        <small class="text-muted d-block">Username</small>
                                        <strong>${username}</strong>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-envelope text-primary me-2"></i>
                                    <div>
                                        <small class="text-muted d-block">Email</small>
                                        <strong>${email}</strong>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-phone text-primary me-2"></i>
                                    <div>
                                        <small class="text-muted d-block">Phone</small>
                                        <strong>${phone}</strong>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <hr class="my-2" style="border-color: rgba(67, 97, 238, 0.1);">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-calendar text-primary me-2"></i>
                                    <div>
                                        <small class="text-muted d-block">Registration Date</small>
                                        <strong>${regDate} at ${regTime}</strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-12">
                <div class="alert alert-info border-0" style="background: linear-gradient(135deg, rgba(13, 202, 240, 0.1) 0%, rgba(13, 110, 253, 0.1) 100%); border-left: 4px solid #0dcaf0 !important;">
                    <div class="d-flex">
                        <div class="me-3">
                            <i class="fas fa-info-circle text-info" style="font-size: 1.2rem;"></i>
                        </div>
                        <div>
                            <h5 class="alert-title text-info mb-2">Ready for Review</h5>
                            <p class="mb-0">This user account is awaiting administrative approval. Review the details above and use the action buttons below to process this application.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Store current user data for actions
    window.currentUserData = {
        id: userId,
        fullName: fullName,
        username: username,
        email: email
    };

    document.getElementById('userDetailsContent').innerHTML = userDetailsHTML;

    // Show action buttons in modal footer
    document.getElementById('approveFromDetailsBtn').style.display = 'inline-block';
    document.getElementById('rejectFromDetailsBtn').style.display = 'inline-block';

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('userDetailsModal'));
    modal.show();
}

// Global variables for confirmation system
let confirmationData = null;

// Show custom confirmation modal
function showConfirmation(action, userId, fullName, username) {
    confirmationData = { action, userId, fullName, username };

    const isApprove = action === 'approve';
    const modalHeader = document.getElementById('confirmModalHeader');
    const modalTitle = document.getElementById('confirmModalTitle');
    const modalBody = document.getElementById('confirmModalBody');
    const confirmBtn = document.getElementById('confirmActionBtn');

    // Set header style and title
    if (isApprove) {
        modalHeader.style.background = 'linear-gradient(135deg, #28a745 0%, #1e7e34 100%)';
        modalHeader.style.color = 'white';
        modalTitle.innerHTML = '<i class="fas fa-check-circle me-2"></i>Approve User Account';
        confirmBtn.className = 'btn btn-success';
        confirmBtn.innerHTML = '<i class="fas fa-check me-2"></i>Yes, Approve';
    } else {
        modalHeader.style.background = 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)';
        modalHeader.style.color = 'white';
        modalTitle.innerHTML = '<i class="fas fa-times-circle me-2"></i>Reject User Account';
        confirmBtn.className = 'btn btn-danger';
        confirmBtn.innerHTML = '<i class="fas fa-times me-2"></i>Yes, Reject';
    }

    // Create confirmation content
    const actionText = isApprove ? 'approve' : 'reject';
    const actionColor = isApprove ? 'success' : 'danger';
    const actionIcon = isApprove ? 'check-circle' : 'times-circle';

    modalBody.innerHTML = `
        <div class="text-center mb-4">
            <div class="avatar avatar-lg mx-auto mb-3" style="background: linear-gradient(135deg, ${isApprove ? '#28a745' : '#dc3545'} 0%, ${isApprove ? '#1e7e34' : '#c82333'} 100%); color: white; font-size: 1.2rem;">
                ${fullName.split(' ').map(n => n[0]).join('').toUpperCase().substring(0, 2)}
            </div>
            <h5 class="mb-2">${fullName}</h5>
            <p class="text-muted mb-0">${username}</p>
        </div>

        <div class="alert alert-${actionColor} border-0" style="background: linear-gradient(135deg, rgba(${isApprove ? '40, 167, 69' : '220, 53, 69'}, 0.1) 0%, rgba(${isApprove ? '30, 126, 52' : '200, 35, 51'}, 0.1) 100%);">
            <div class="d-flex">
                <div class="me-3">
                    <i class="fas fa-${actionIcon} text-${actionColor}" style="font-size: 1.5rem;"></i>
                </div>
                <div>
                    <h5 class="alert-title text-${actionColor} mb-2">Confirm ${actionText.charAt(0).toUpperCase() + actionText.slice(1)}</h5>
                    <p class="mb-0">
                        Are you sure you want to <strong>${actionText}</strong> this user account?
                        ${!isApprove ? '<br><small class="text-danger"><strong>Warning:</strong> This action cannot be undone.</small>' : ''}
                    </p>
                </div>
            </div>
        </div>

        <div class="row text-center">
            <div class="col-6">
                <div class="border rounded p-2">
                    <i class="fas fa-user text-primary mb-1"></i>
                    <div class="small text-muted">User</div>
                    <div class="fw-bold">${fullName}</div>
                </div>
            </div>
            <div class="col-6">
                <div class="border rounded p-2">
                    <i class="fas fa-cog text-primary mb-1"></i>
                    <div class="small text-muted">Action</div>
                    <div class="fw-bold text-${actionColor}">${actionText.charAt(0).toUpperCase() + actionText.slice(1)}</div>
                </div>
            </div>
        </div>
    `;

    // Show confirmation modal
    const modal = new bootstrap.Modal(document.getElementById('confirmationModal'));
    modal.show();
}

// Execute confirmed action
function executeConfirmedAction() {
    if (!confirmationData) return;

    const { action, userId } = confirmationData;

    // Create and submit form
    const form = document.createElement('form');
    form.method = 'POST';
    form.style.display = 'none';

    const userIdInput = document.createElement('input');
    userIdInput.type = 'hidden';
    userIdInput.name = 'user_id';
    userIdInput.value = userId;

    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = action;

    form.appendChild(userIdInput);
    form.appendChild(actionInput);
    document.body.appendChild(form);
    form.submit();
}

// Approve user from details modal
function approveUserFromDetails() {
    if (window.currentUserData) {
        const { id, fullName, username } = window.currentUserData;
        // Hide details modal first
        const detailsModal = bootstrap.Modal.getInstance(document.getElementById('userDetailsModal'));
        detailsModal.hide();
        // Show confirmation
        setTimeout(() => {
            showConfirmation('approve', id, fullName, username);
        }, 300);
    }
}

// Reject user from details modal
function rejectUserFromDetails() {
    if (window.currentUserData) {
        const { id, fullName, username } = window.currentUserData;
        // Hide details modal first
        const detailsModal = bootstrap.Modal.getInstance(document.getElementById('userDetailsModal'));
        detailsModal.hide();
        // Show confirmation
        setTimeout(() => {
            showConfirmation('reject', id, fullName, username);
        }, 300);
    }
}

// Toggle select all checkboxes
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.user-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// Bulk approve selected users
function bulkApprove() {
    const selectedUsers = getSelectedUsers();
    if (selectedUsers.length === 0) {
        alert('Please select at least one user to approve.');
        return;
    }

    if (confirm(`Are you sure you want to approve ${selectedUsers.length} user(s)?`)) {
        // Process bulk approval
        processBulkAction('approve', selectedUsers);
    }
}

// Bulk reject selected users
function bulkReject() {
    const selectedUsers = getSelectedUsers();
    if (selectedUsers.length === 0) {
        alert('Please select at least one user to reject.');
        return;
    }

    if (confirm(`Are you sure you want to reject ${selectedUsers.length} user(s)? This action cannot be undone.`)) {
        // Process bulk rejection
        processBulkAction('reject', selectedUsers);
    }
}

// Get selected user IDs
function getSelectedUsers() {
    const checkboxes = document.querySelectorAll('.user-checkbox:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// Process bulk action
function processBulkAction(action, userIds) {
    // Create a form to submit bulk action
    const form = document.createElement('form');
    form.method = 'POST';
    form.style.display = 'none';

    // Add action field
    const actionField = document.createElement('input');
    actionField.type = 'hidden';
    actionField.name = 'bulk_action';
    actionField.value = action;
    form.appendChild(actionField);

    // Add user IDs
    userIds.forEach(id => {
        const idField = document.createElement('input');
        idField.type = 'hidden';
        idField.name = 'user_ids[]';
        idField.value = id;
        form.appendChild(idField);
    });

    document.body.appendChild(form);
    form.submit();
}
</script>

<?php include 'includes/admin-footer.php'; ?>
